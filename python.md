// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22"
        classpath("com.google.dagger:hilt-android-gradle-plugin:2.49")
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.1'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath("androidx.navigation:navigation-safe-args-gradle-plugin:2.5.1")
        classpath 'com.google.gms:google-services:4.3.13'

    }
}

plugins {
    id 'com.android.application' version '7.2.2' apply false
    id 'com.android.library' version '7.2.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.8.0' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'androidx.navigation.safeargs'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'com.google.firebase.crashlytics'
    id 'com.google.gms.google-services'
}

android {
    buildToolsVersion = "30.0.3"
    compileSdk 35

    defaultConfig {
        applicationId "pdf.reader.editor.pdfviewer.pdfreader"
        minSdk 24
        targetSdk 35
        versionCode 23
        versionName "1.2.3"
        setProperty("archivesBaseName", "pdf_editor" + "_vc_" + versionCode + "_vn_" + versionName + "_")

//        ndk {
//            abiFilters 'armeabi-v7a' , 'arm64-v8a'
//        }
        externalNativeBuild {
            cmake {
                // Pass arguments to CMake. Use -D<OPTION_NAME>=<VALUE>
                arguments "-D SUPPORT_GPROOF=OFF"
            }
        }
    }


    externalNativeBuild {
//        ndkBuild {
//            path file('src/main/jni/Android.mk')
//        }
        cmake {
            path file('src/main/jni/CMakeLists.txt')
        }
    }

//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['libs']
//        }
//    }

    signingConfigs {
        release {
            storeFile file('../keystore/digizone.jks')
            storePassword 'digizone'
            keyAlias 'digizone'
            keyPassword 'digizone'
        }
    }

    buildTypes {

        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

           
        }

        debug {
            minifyEnabled false
         
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
        buildConfig true
    }

    dexOptions {
        incremental true
        javaMaxHeapSize "6g"
    }

    packagingOptions {
        pickFirst '**/*.so'
        pickFirst 'versionchanges.txt'
        pickFirst 'logback.xml'
        pickFirst 'CertPathReviewerMessages_de.properties'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'jetified-geronimo-stax-api_1.0_spec-1.0'
        exclude 'META-INF/INDEX.LIST'
        pickFirst 'META-INF/DEPENDENCIES'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude("META-INF/*.kotlin_module")


    }

    configurations {
        all {
            resolutionStrategy {
                //force 'com.itextpdf:pdfocr-tesseract4:2.0.1'
                force 'com.itextpdf:kernel:7.1.9'
                force 'org.apache.poi:poi-ooxml:3.6'
            }
        }
    }

    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    ndkVersion '25.1.8937393'

    namespace 'pdf.reader.editor.pdfviewer.pdfreader'
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    api 'androidx.core:core-ktx:1.13.1'

    implementation 'androidx.appcompat:appcompat:1.7.1'
    api 'com.google.android.material:material:1.6.1'
    api 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation project(':medialoader')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    api "androidx.recyclerview:recyclerview:1.2.1"
//    implementation project(":pdfviewer")
//    api project(path: ':xsearchview')
//    api project(path: ':recyclerviewfastscroller')

    // Fast Scroll RecyclerView
    api "io.github.l4digital:fastscroll:2.1.0"

    // Navigation Component
    api "androidx.navigation:navigation-fragment-ktx:2.9.2"
    api "androidx.navigation:navigation-ui-ktx:2.9.2"
    implementation "androidx.fragment:fragment-ktx:1.5.2"


    // Hilt DI
    api("com.google.dagger:hilt-android:2.48")
    kapt("com.google.dagger:hilt-android-compiler:2.48")

    // Firebase
    api platform('com.google.firebase:firebase-bom:30.3.1')
    api 'com.google.firebase:firebase-analytics-ktx'
    api 'com.google.firebase:firebase-config-ktx'
//    api 'com.google.firebase:firebase-database-ktx'
    api 'com.google.firebase:firebase-crashlytics'

    // Coroutines
    api "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4"
    api "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"
    api 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.6.4'

    // ROOM
    api("androidx.room:room-runtime:2.4.3")
    kapt("androidx.room:room-compiler:2.4.3")
    api("androidx.room:room-ktx:2.4.3")

    // Lifecycle
    def lifecycle_version = "2.6.2"
    api "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    api "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    api "androidx.lifecycle:lifecycle-common-java8:$lifecycle_version"
    api "androidx.lifecycle:lifecycle-runtime-ktx:2.5.1"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"

    // Google AdMob
    api 'com.google.android.gms:play-services-ads:21.4.0'
    implementation("com.google.android.ump:user-messaging-platform:2.1.0")

    // Preferences DataStore
    api "androidx.datastore:datastore-preferences:1.0.0-alpha02"

    // Proto DataStore
    api "androidx.datastore:datastore-core:1.0.0-alpha02"

    //Timber
    api 'com.jakewharton.timber:timber:5.0.1'

    // ITEXT
    api 'com.itextpdf:itextpdf:5.0.6'
    api 'com.itextpdf:kernel:7.1.9'
    api 'com.itextpdf:html2pdf:2.1.6'

    // apache PDFBOX
//    api 'org.apache.pdfbox:pdfbox:2.0.0-RC3'
//    api 'net.sf.cssbox:pdf2dom:1.2'

//    implementation 'com.viliussutkus89:pdf2htmlex-android:0.18.13'
    api 'com.github.pramodkr123:ConvertWebViewToPdfDemo:1.0.5'

    // for ripple effect
    api 'com.balysv:material-ripple:1.0.2'

    api 'androidx.work:work-runtime:2.7.1'

    // PDFIUM
    api 'com.github.barteksc:pdfium-android:1.9.0'

    //ssp & sdp
    api 'com.intuit.sdp:sdp-android:1.0.6'
    api 'com.intuit.ssp:ssp-android:1.0.6'

    // Rich Editor
    implementation 'jp.wasabeef:richeditor-android:2.0.0'

    // Coil
    api("io.coil-kt:coil:2.1.0")

    // Lottie Animation
    api "com.airbnb.android:lottie:4.2.2"

    // Media Loader
    // implementation 'com.jiajunhui.xapp.medialoader:medialoader:1.2.1'

    // Retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    implementation 'com.getkeepsafe.relinker:relinker:1.4.5'

    implementation 'com.h6ah4i.android.widget.verticalseekbar:verticalseekbar:1.0.0'

    //ShimmerEffect
    api 'com.facebook.shimmer:shimmer:0.5.0'

    //InAppUpdates
    api 'com.google.android.play:app-update-ktx:2.1.0'
}

apply plugin: 'com.android.library'

version = "1.2.1"

android {

# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app"s APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
android.enableJetifier=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
org.gradle.parallel=true

