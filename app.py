# class Laptop:
#     def __init__(self,  CPU, RAM, Storage, Graphic_Card):
#         self.CPU = CPU
#         self.RAM = RAM
#         self.Storage = Storage
#         self.Graphic_Card = Graphic_Card

# L1 = Laptop("Intel(i3, i5, i7, i9)", "8GB", "SSD", "Integrated GPU")
# print(L1.CPU)

# class using **kwargs ---> kwargs represent dictionary means keyword argument/key value pair
# class Laptop:
#     def __init__(self, **kwargs):
#         print(kwargs)

# L1 = Laptop(CPU="Intel i3", RAM="8GB", Storage="SSD", Graphic_Card="Integrated GPU")
# print(L1)

# class using *args ----> represent tuple
# class Laptop:
#     def __init__(self, *args):
#         print(args)

# L1 = Laptop("CPU:Intel i3", "RAM:8GB", "Storage:SSD", "Graphic_Card:Integrated GPU")
# print(L1)

