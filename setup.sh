#!/bin/bash

# =============================================================================
# Android Development Environment Setup Script
# =============================================================================
# This script configures the development environment for the PDF Editor project
# with the specified Android development tools and versions.
#
# Project Structure (based on python.md analysis):
# - Main app module: PDF Reader/Editor application
# - Sub-modules: medialoader
# - Native components: CMake/JNI integration
# - Key features: PDF processing, Firebase integration, Hilt DI
#
# Required Specifications:
# - Android Studio: 2025.1.2 (Narwhal Feature Drop)
# - Android Gradle Plugin (AGP): 8.11.1
# - Kotlin: 2.1.0
# - KSP: 2.1.0-1.0.29
# - JVM Target: 17
# - Compile/Target SDK: 35, Min SDK: 24
# - Gradle: 8.11.1
# =============================================================================

set -e  # Exit on any error
set -u  # Exit on undefined variables

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ANDROID_STUDIO_VERSION="2025.1.2"
AGP_VERSION="8.11.1"
KOTLIN_VERSION="2.1.0"
KSP_VERSION="2.1.0-1.0.29"
GRADLE_VERSION="8.11.1"
JVM_TARGET="17"
COMPILE_SDK="35"
TARGET_SDK="35"
MIN_SDK="24"
NDK_VERSION="25.1.8937393"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
handle_error() {
    log_error "Setup failed at line $1. Exiting..."
    exit 1
}

trap 'handle_error $LINENO' ERR

# Check if running on supported OS
check_os() {
    log_info "Checking operating system compatibility..."
    
    case "$(uname -s)" in
        Linux*)     OS=Linux;;
        Darwin*)    OS=Mac;;
        CYGWIN*|MINGW*|MSYS*) OS=Windows;;
        *)          OS="UNKNOWN";;
    esac
    
    if [ "$OS" = "UNKNOWN" ]; then
        log_error "Unsupported operating system: $(uname -s)"
        exit 1
    fi
    
    log_success "Operating system detected: $OS"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Java 17
    if command -v java >/dev/null 2>&1; then
        JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [ "$JAVA_VERSION" -ge 17 ]; then
            log_success "Java $JAVA_VERSION found"
        else
            log_warning "Java 17+ required, found Java $JAVA_VERSION"
            install_java
        fi
    else
        log_warning "Java not found"
        install_java
    fi
    
    # Check Git
    if ! command -v git >/dev/null 2>&1; then
        log_error "Git is required but not installed"
        exit 1
    fi
    log_success "Git found"
}

# Install Java 17
install_java() {
    log_info "Installing Java 17..."
    
    case "$OS" in
        Linux)
            if command -v apt-get >/dev/null 2>&1; then
                sudo apt-get update
                sudo apt-get install -y openjdk-17-jdk
            elif command -v yum >/dev/null 2>&1; then
                sudo yum install -y java-17-openjdk-devel
            else
                log_error "Unsupported Linux distribution"
                exit 1
            fi
            ;;
        Mac)
            if command -v brew >/dev/null 2>&1; then
                brew install openjdk@17
                echo 'export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"' >> ~/.zshrc
            else
                log_error "Homebrew required for macOS installation"
                exit 1
            fi
            ;;
        Windows)
            log_warning "Please install Java 17 manually from https://adoptium.net/"
            ;;
    esac
    
    log_success "Java 17 installation completed"
}

# Setup Android SDK
setup_android_sdk() {
    log_info "Setting up Android SDK..."
    
    # Set Android SDK path
    if [ -z "${ANDROID_HOME:-}" ]; then
        case "$OS" in
            Linux|Mac)
                export ANDROID_HOME="$HOME/Android/Sdk"
                ;;
            Windows)
                export ANDROID_HOME="$HOME/AppData/Local/Android/Sdk"
                ;;
        esac
    fi
    
    # Create SDK directory
    mkdir -p "$ANDROID_HOME"
    
    # Download command line tools if not present
    if [ ! -d "$ANDROID_HOME/cmdline-tools" ]; then
        log_info "Downloading Android command line tools..."
        
        case "$OS" in
            Linux)
                TOOLS_URL="https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip"
                ;;
            Mac)
                TOOLS_URL="https://dl.google.com/android/repository/commandlinetools-mac-11076708_latest.zip"
                ;;
            Windows)
                TOOLS_URL="https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip"
                ;;
        esac
        
        curl -o cmdline-tools.zip "$TOOLS_URL"
        unzip -q cmdline-tools.zip -d "$ANDROID_HOME"
        mv "$ANDROID_HOME/cmdline-tools" "$ANDROID_HOME/cmdline-tools-temp"
        mkdir -p "$ANDROID_HOME/cmdline-tools/latest"
        mv "$ANDROID_HOME/cmdline-tools-temp"/* "$ANDROID_HOME/cmdline-tools/latest/"
        rm -rf "$ANDROID_HOME/cmdline-tools-temp" cmdline-tools.zip
    fi
    
    # Set PATH
    export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"
    
    log_success "Android SDK setup completed"
}

# Install Android SDK components
install_sdk_components() {
    log_info "Installing Android SDK components..."
    
    # Accept licenses
    yes | sdkmanager --licenses >/dev/null 2>&1 || true
    
    # Install required components
    sdkmanager "platform-tools"
    sdkmanager "platforms;android-$COMPILE_SDK"
    sdkmanager "build-tools;34.0.0"
    sdkmanager "ndk;$NDK_VERSION"
    sdkmanager "cmake;3.22.1"
    
    log_success "Android SDK components installed"
}

# Setup Gradle
setup_gradle() {
    log_info "Setting up Gradle $GRADLE_VERSION..."
    
    # Download and install Gradle
    GRADLE_HOME="$HOME/.gradle/wrapper/dists/gradle-$GRADLE_VERSION"
    
    if [ ! -d "$GRADLE_HOME" ]; then
        mkdir -p "$GRADLE_HOME"
        cd "$GRADLE_HOME"
        
        curl -L "https://services.gradle.org/distributions/gradle-$GRADLE_VERSION-bin.zip" -o gradle.zip
        unzip -q gradle.zip
        rm gradle.zip
        
        # Find the extracted directory
        GRADLE_DIR=$(find . -maxdepth 1 -type d -name "gradle-*" | head -1)
        if [ -n "$GRADLE_DIR" ]; then
            export GRADLE_HOME="$GRADLE_HOME/$GRADLE_DIR"
            export PATH="$GRADLE_HOME/bin:$PATH"
        fi
    fi
    
    log_success "Gradle $GRADLE_VERSION setup completed"
}

# Update project configuration files
update_project_config() {
    log_info "Updating project configuration files..."

    # Update root build.gradle
    if [ -f "build.gradle" ]; then
        log_info "Updating root build.gradle..."

        # Backup original file
        cp build.gradle build.gradle.backup

        # Update AGP version
        sed -i.tmp "s/classpath 'com.android.tools.build:gradle:[^']*'/classpath 'com.android.tools.build:gradle:$AGP_VERSION'/" build.gradle

        # Update Kotlin version
        sed -i.tmp "s/classpath \"org.jetbrains.kotlin:kotlin-gradle-plugin:[^\"]*\"/classpath \"org.jetbrains.kotlin:kotlin-gradle-plugin:$KOTLIN_VERSION\"/" build.gradle

        # Update plugin versions
        sed -i.tmp "s/id 'com.android.application' version '[^']*'/id 'com.android.application' version '$AGP_VERSION'/" build.gradle
        sed -i.tmp "s/id 'com.android.library' version '[^']*'/id 'com.android.library' version '$AGP_VERSION'/" build.gradle
        sed -i.tmp "s/id 'org.jetbrains.kotlin.android' version '[^']*'/id 'org.jetbrains.kotlin.android' version '$KOTLIN_VERSION'/" build.gradle

        # Clean up temp files
        rm -f build.gradle.tmp

        log_success "Root build.gradle updated"
    fi

    # Update app build.gradle
    if [ -f "app/build.gradle" ]; then
        log_info "Updating app/build.gradle..."

        # Backup original file
        cp app/build.gradle app/build.gradle.backup

        # Update compile and target SDK
        sed -i.tmp "s/compileSdk [0-9]*/compileSdk $COMPILE_SDK/" app/build.gradle
        sed -i.tmp "s/targetSdk [0-9]*/targetSdk $TARGET_SDK/" app/build.gradle
        sed -i.tmp "s/minSdk [0-9]*/minSdk $MIN_SDK/" app/build.gradle

        # Update JVM target
        sed -i.tmp "s/jvmTarget = '[^']*'/jvmTarget = '$JVM_TARGET'/" app/build.gradle
        sed -i.tmp "s/sourceCompatibility JavaVersion.VERSION_[0-9_]*/sourceCompatibility JavaVersion.VERSION_$JVM_TARGET/" app/build.gradle
        sed -i.tmp "s/targetCompatibility JavaVersion.VERSION_[0-9_]*/targetCompatibility JavaVersion.VERSION_$JVM_TARGET/" app/build.gradle

        # Add KSP plugin if not present
        if ! grep -q "id 'com.google.devtools.ksp'" app/build.gradle; then
            sed -i.tmp "/id 'kotlin-kapt'/a\\    id 'com.google.devtools.ksp' version '$KSP_VERSION'" app/build.gradle
        fi

        # Clean up temp files
        rm -f app/build.gradle.tmp

        log_success "App build.gradle updated"
    fi

    # Update gradle wrapper
    if [ -f "gradle/wrapper/gradle-wrapper.properties" ]; then
        log_info "Updating gradle wrapper properties..."

        # Backup original file
        cp gradle/wrapper/gradle-wrapper.properties gradle/wrapper/gradle-wrapper.properties.backup

        # Update Gradle version
        sed -i.tmp "s/distributionUrl=.*gradle-[^-]*-bin.zip/distributionUrl=https\\\\://services.gradle.org\/distributions\/gradle-$GRADLE_VERSION-bin.zip/" gradle/wrapper/gradle-wrapper.properties

        # Clean up temp files
        rm -f gradle/wrapper/gradle-wrapper.properties.tmp

        log_success "Gradle wrapper properties updated"
    fi

    # Update gradle.properties
    if [ -f "gradle.properties" ]; then
        log_info "Updating gradle.properties..."

        # Backup original file
        cp gradle.properties gradle.properties.backup

        # Add or update JVM args for better performance
        if ! grep -q "org.gradle.jvmargs" gradle.properties; then
            echo "org.gradle.jvmargs=-Xmx4g -Dfile.encoding=UTF-8" >> gradle.properties
        else
            sed -i.tmp "s/org.gradle.jvmargs=.*/org.gradle.jvmargs=-Xmx4g -Dfile.encoding=UTF-8/" gradle.properties
        fi

        # Enable parallel builds
        if ! grep -q "org.gradle.parallel" gradle.properties; then
            echo "org.gradle.parallel=true" >> gradle.properties
        fi

        # Enable configuration cache
        if ! grep -q "org.gradle.configuration-cache" gradle.properties; then
            echo "org.gradle.configuration-cache=true" >> gradle.properties
        fi

        # Clean up temp files
        rm -f gradle.properties.tmp

        log_success "gradle.properties updated"
    fi
}

# Setup environment variables
setup_environment() {
    log_info "Setting up environment variables..."

    # Determine shell config file
    case "$SHELL" in
        */zsh)
            SHELL_CONFIG="$HOME/.zshrc"
            ;;
        */bash)
            SHELL_CONFIG="$HOME/.bashrc"
            ;;
        *)
            SHELL_CONFIG="$HOME/.profile"
            ;;
    esac

    # Create backup
    if [ -f "$SHELL_CONFIG" ]; then
        cp "$SHELL_CONFIG" "$SHELL_CONFIG.backup"
    fi

    # Add Android environment variables
    cat >> "$SHELL_CONFIG" << EOF

# Android Development Environment
export ANDROID_HOME="$ANDROID_HOME"
export ANDROID_SDK_ROOT="\$ANDROID_HOME"
export PATH="\$ANDROID_HOME/cmdline-tools/latest/bin:\$ANDROID_HOME/platform-tools:\$ANDROID_HOME/emulator:\$PATH"

# Java
export JAVA_HOME=\$(dirname \$(dirname \$(readlink -f \$(which java))))

# Gradle
export GRADLE_HOME="$GRADLE_HOME"
export PATH="\$GRADLE_HOME/bin:\$PATH"

EOF

    log_success "Environment variables configured in $SHELL_CONFIG"
}

# Verify installation
verify_installation() {
    log_info "Verifying installation..."

    # Source the shell config to get updated PATH
    case "$SHELL" in
        */zsh)
            source "$HOME/.zshrc" 2>/dev/null || true
            ;;
        */bash)
            source "$HOME/.bashrc" 2>/dev/null || true
            ;;
    esac

    # Check Java
    if java -version 2>&1 | grep -q "17\|18\|19\|20\|21"; then
        log_success "Java 17+ verified"
    else
        log_warning "Java 17+ verification failed"
    fi

    # Check Android SDK
    if [ -d "$ANDROID_HOME" ] && [ -f "$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager" ]; then
        log_success "Android SDK verified"
    else
        log_warning "Android SDK verification failed"
    fi

    # Check Gradle
    if command -v gradle >/dev/null 2>&1; then
        GRADLE_VER=$(gradle --version | grep "Gradle" | cut -d' ' -f2)
        log_success "Gradle $GRADLE_VER verified"
    else
        log_warning "Gradle verification failed"
    fi

    # Test project build (if in project directory)
    if [ -f "build.gradle" ] && [ -f "gradlew" ]; then
        log_info "Testing project build..."
        if ./gradlew clean build --dry-run >/dev/null 2>&1; then
            log_success "Project build test passed"
        else
            log_warning "Project build test failed - manual verification needed"
        fi
    fi
}

# Main execution
main() {
    log_info "Starting Android Development Environment Setup"
    log_info "Target specifications:"
    log_info "  - Android Studio: $ANDROID_STUDIO_VERSION"
    log_info "  - AGP: $AGP_VERSION"
    log_info "  - Kotlin: $KOTLIN_VERSION"
    log_info "  - KSP: $KSP_VERSION"
    log_info "  - Gradle: $GRADLE_VERSION"
    log_info "  - JVM Target: $JVM_TARGET"
    log_info "  - SDK: Compile $COMPILE_SDK, Target $TARGET_SDK, Min $MIN_SDK"
    echo

    check_os
    check_prerequisites
    setup_android_sdk
    install_sdk_components
    setup_gradle
    update_project_config
    setup_environment
    verify_installation

    echo
    log_success "=== Setup completed successfully! ==="
    log_info "Next steps:"
    log_info "1. Restart your terminal or run: source $SHELL_CONFIG"
    log_info "2. Install Android Studio $ANDROID_STUDIO_VERSION manually"
    log_info "3. Open the project in Android Studio"
    log_info "4. Sync project with Gradle files"
    log_info "5. Run './gradlew clean build' to verify everything works"
    echo
    log_warning "Note: Configuration backups were created with .backup extension"
}

# Run main function
main "$@"
